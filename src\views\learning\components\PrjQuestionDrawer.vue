<template>
  <div class="warpper">
    <el-drawer
      class="drawer-container"
      v-model="isDialogShow"
      size="50%"
      direction="ltr"
      :with-header="false"
      @close="handleClose"
      :z-index="10"
    >
      <div class="header">
        <span class="iconfont icon-icon_close" @click="isDialogShow = false"></span>
        <div class="title">提问</div>
        <el-divider class="divider" />
      </div>
      <div class="body">
        <div class="info">
          <span class="name"> {{ userInfo.username }} </span>
          的提问
        </div>
        <div class="question" v-html="curQuestion.relatedText" />
        <!-- <el-input v-model="input" class="keyword" /> -->

        <div class="type">
          <div class="choice">选择类型:</div>
          <div>
            <CmpButton
              v-for="(value, key) in qTypeDict"
              :key="key"
              type="info"
              class="type-btn"
              :class="{ hightlight: curQuestion.qType == value }"
              @click="handleChangeQType(value)"
            >
              {{ key }}
            </CmpButton>
          </div>
        </div>
        <div class="content">
          <InlineEditor
            v-model="description"
            :rows="3"
            type="textarea"
            class="description"
            placeholder="请输入问题内容"
            v-if="showQDescription"
          ></InlineEditor>
          <template v-else>
            <InlineEditor v-model="input"></InlineEditor>
            <span class="qtype"> {{ qTypeMap.get(curQuestion.qType) }}</span>
          </template>
        </div>

        <div class="submit">
          <CmpButton class="submit-btn" type="primary" @click="handleSubmit">提交问题</CmpButton>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { QuestionType, qMode, qModeDict, qTypeDict, qTypeMap } from '@/types/constant';
import { useUserStore } from '@/stores/user';
// import CkEditorInline from '@/components/CkEditor/CkEditorInline.vue';
// import CkEditorClassic from '@/components/CkEditor/CkEditorClassic.vue';
import InlineEditor from '@/components/editors/VditorInline.vue';
import ClassicEditor from '@/components/editors/Vditor.vue';

// 打包注释
// import editor from "@/views/common/inlineCKEditor.vue";
import CmpButton from '@/components/CmpButton.vue';
const input = ref();
const description = ref();
const editorRef = ref();
const userStore = useUserStore();
const { userInfo } = storeToRefs(userStore);
const ruleFormRef = ref<FormInstance>();
const curMode = ref<number>(); // 0新增|1编辑|2只读
const isDialogShow = ref(false);
const dialogTitle = ref();
const emits = defineEmits(['submit', 'open', 'close']);
const displayQuestionList = ref<[]>();
const showQDescription = ref(false);
const curQuestion = reactive({
  qId: -1,
  relatedText: '',
  qContent: '',
  qType: QuestionType.what, // 是什么 | 为什么
  qMode: qMode.necessary, // 必要 | 参考
  klg: [],
  explanation: '',
  questionDescription: ''
});
// TODO 表单校验有一点问题
const rules = reactive<FormRules>({
  //   qContent: [{required: true,message: '请输入关键字',trigger: 'blur'}],
  //   qType: [{required: true,message: '请选择问题类型',trigger: 'change'}],
  //   qMode: [{required: true,message: '请选择问题属性',trigger: 'change'}],
  //   klg: [{required: false,message: '请选择知识点',trigger: 'change'}],
  //   explanation: [{required: false,message: '请输入问题解析',trigger: 'blur'}],
});
const handleClose = () => {
  description.value = '';
  showQDescription.value = false;
  isDialogShow.value = false;
  //emits('close');
};

const handleChangeQType = (value: number) => {
  curQuestion.qType = value;
  if (value == QuestionType.what) {
    curQuestion.klg = [curQuestion.klg[0]];
  }
  if (value == QuestionType.open) {
    showQDescription.value = true;
  } else {
    showQDescription.value = false;
  }
};

const handleSubmit = () => {
  curQuestion.qContent = input.value;
  if (curQuestion.qType == QuestionType.open) {
    curQuestion.questionDescription = description.value;
  }
  emits('submit', curQuestion);
  description.value = '';
  showQDescription.value = false;
  handleClose();
};

const showDialog = (questionList, mode: number) => {
  console.log(curQuestion);
  displayQuestionList.value = questionList;
  curMode.value = mode; // 2:不可操作
  Object.assign(curQuestion, questionList[0]);
  console.log(curQuestion);
  switch (curMode.value) {
    case 0:
      dialogTitle.value = '添加问题';
      break;
    case 1:
      dialogTitle.value = '编辑问题';
      break;
    case 2:
      dialogTitle.value = '展示问题';
      break;
    default:
      console.log('问题弹窗mode异常');
      break;
  }
  isDialogShow.value = true;
  input.value = curQuestion.qContent;
  console.log(curQuestion);
  //   nextTick(() => {
  //     editorRef.value.setData(curQuestion.relatedText, curMode.value == 2);
  //   })
  // emits('open');
};

defineExpose({
  showDialog
});
</script>

<style scoped lang="less">
.warpper {
  &:deep(.el-drawer__body) {
    padding: 10px 10px 10px 10px;
    margin: 0;
    border-bottom: 1px solid #dddddd;
    color: #333333;
    font-family:
      '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;
    font-size: 16px;
    font-weight: 400;
  }
  .drawer-container {
    .icon-icon_close {
      position: absolute;
      right: 10px;
      top: 10px;
    }
    .header {
      .title {
        font-weight: 700;
      }
      .divider {
        margin: 10px 0;
      }
    }
    .body {
      .info {
        font-size: 12px;
        .name {
          font-weight: 700;
          margin-right: 10px;
        }
      }
      .question {
        margin: 10px 0;
        padding: 5px 10px;
        font-size: 14px;
        color: var(--color-theme-project);
        background-color: var(--color-second);
      }
      .keyword {
        margin: 10px 0px;
      }
      .content {
        display: flex;
        flex-direction: column;
        .qtype {
          display: flex;
          justify-content: flex-end;
        }
      }
      .type {
        display: flex;
        flex-direction: row;
        // justify-content: space-between;
        justify-content: space-between;
        .choice {
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .type-btn {
          margin: 10px 0px 10px 0;
          width: 150px;
          height: 30px;
          font-size: var(--fontsize-middle-project);
          border: 1px solid var(--color-theme-project);
          background-color: var(--color-second);
          color: var(--color-theme-project);
          &:hover {
            color: rgba(255, 255, 255, 0.996);
            background-color: var(--color-theme-project);
            border: 1px solid transparent;
          }
        }
        .type-btn.hightlight {
          color: rgba(255, 255, 255, 0.996);
          background-color: var(--color-theme-project);
          border: 1px solid transparent;
        }
      }
      .description {
      }
      .submit {
        display: flex;
        flex-direction: row-reverse;
        .submit-btn {
          margin: 10px 10px;
          border-radius: 2px;
          width: 80px;
          height: 25px;
          font-size: var(--fontsize-middle-project);
        }
      }
    }
  }
}
</style>
