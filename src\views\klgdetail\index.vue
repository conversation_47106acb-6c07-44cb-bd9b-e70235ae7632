<script setup lang="ts">
// import CkEditorClassic from '@/components/CkEditor/CkEditorClassic.vue';
import ClassicEditor from '@/components/editors/Vditor.vue';
import InlineEditor from '@/components/editors/VditorInline.vue';
import CmpButton from '@/components/CmpButton.vue';
import { getklgdetailApi, saveFeedBackApi, getPreklglistApi, getKlgRecommend } from '@/apis/klgdetail';
import KnowledgeContent from './klgContent.vue'; // 知识内容组件
import MapContent from './MapContent.vue';
import EvaluationContent from './EvaluationContent.vue'; // 讲解测评组件
import ApplicationContent from './ApplicationContent.vue'; // 应用场景组件
import ErrorDialog from '@/components/ErrorDialog.vue';
import {
  Document,
  EditPen,
  Opportunity,
  Expand,
  Fold,
  ArrowRightBold,
  Orange
} from '@element-plus/icons-vue';

const route = useRoute();
const klgCode = route.query.klgCode as string;
console.log('klgcodedede', klgCode);
provide('klgCode', klgCode);

const currentContent = ref('knowledge'); // 默认显示知识内容
const isFolded = ref(false);
const ready = ref(false);
const NoPermisson = ref(false);
const errorInfo = ref('');

//知识点详情数据类型
const klgDetail = ref<KlgdetailData>({
  klgTitle: '',
  status: '已掌握', // 可选
  tags: ['Verlog语言', '芯片设计'],
  mastered: 2,
  total: 20,
  masterPercent: 10,
  preLearned: 0,
  preTotal: 77,
  prePercent: 0,
  provider: '',
  createTime: '',
  field: '',
  sysTitles: '',
  notice: '',
  projectList: [{ userCoverPic: '', uniqueCode: '' }],
  reference: [{ refid: 0, indexPage: '', cntName: '' }]
});
onMounted(async () => {
  try {
    const response = await getklgdetailApi(klgCode);
    console.log(klgDetail.value)
    klgDetail.value.provider = response.data.klgDetail.provider;
    klgDetail.value.createTime = response.data.klgDetail.creatTime;
    klgDetail.value.field = response.data.klgDetail.areaList[0].areaName;
    klgDetail.value.sysTitles = response.data.klgDetail.synonym;
    klgDetail.value.notice = response.data.klgDetail.cnt;
    klgDetail.value.reference = response.data.klgDetail.referenceList;
    klgDetail.value.mastered = response.data.klgDetail.exerciseProgress;
    klgDetail.value.total = response.data.klgDetail.exerciseGoal;
    klgDetail.value.preLearned = response.data.klgDetail.preKlgProgress;
    klgDetail.value.preTotal = response.data.klgDetail.preKlgCount;
    if (response.data.klgDetail.areaList && Array.isArray(response.data.klgDetail.areaList)) {
      klgDetail.value.tags = response.data.klgDetail.areaList.map((item: any) => item.areaName).filter(Boolean);
    }
    if (response.data.klgDetail.status === 0) {
      klgDetail.value.status = '未学习';
    } else if (response.data.klgDetail.status === 1) {
      klgDetail.value.status = '已掌握';
    } else {
      klgDetail.value.status = '全掌握';
    }

    klgDetail.value.klgTitle = response.data.klgDetail.klgTitle;

    //const res1 = await getKlgRecommend(klgCode, 1, "0")
    //console.log('poi')
    //console.log(res1.data);
    //console.log('jkl')
    //recommends.value = res1.data;
    //console.log(recommends.value)
    
    ready.value = true;
  } catch (error) {
    console.error('获取数据失败:', error);
    errorInfo.value = '加载知识点详情失败，请刷新重试';
    NoPermisson.value = true;
    ready.value = true;
  }
});

const tooltipContent = computed(() => {
  if (klgDetail.value.status === '未学习') {
    return '完成本知识的测评后，状态由"未学习"变成"已掌握"。';
  }
  if (klgDetail.value.status === '已掌握') {
    return '本知识所有前驱知识都变成"全掌握"后，状态由"已掌握"会变成"全掌握"。';
  }
  return '';
});

//6/26新加
const recommends = ref<string>([])

const currentActive = ref('knowledge');
const setActive = (content: string) => {
  currentContent.value = content;
  currentActive.value = content;
};
const currentContentComponent = computed(() => {
  if (currentContent.value == 'knowledge') {
    return {
      component: KnowledgeContent,
      props: { klgDetail: klgDetail.value, klgCode: klgCode }
    };
  } else if (currentContent.value == 'evaluation') {
    return {
      component: EvaluationContent,
      props: {recommends: recommends.value}
    };
  } else if (currentContent.value == 'application') {
    return {
      component: ApplicationContent,
      props: {}
    };
  } else if (currentContent.value == 'map') {
    return {
      component: MapContent,
      props: { klgDetail: klgDetail.value, klgCode: klgCode }
    };
  }
  return null;
});

const toggleNavbar = () => {
  isFolded.value = !isFolded.value;
};

// 参考文献数据类型
interface referenceData {
  refid: number;
  cntName: string;
  indexPage: string;
}
// 相关项目数据类型
interface projectData {
  userCoverPic: string;
  uniqueCode: string;
}
// 知识详情数据类型
interface KlgdetailData {
  klgTitle: string;
  status: string;
  tags: string[];
  mastered: number;
  total: number;
  masterPercent: number;
  preLearned: number;
  preTotal: number;
  prePercent: number;
  provider: string;
  createTime: string;
  field: string;
  sysTitles: string;
  notice: string;
  projectList: Array<projectData>;
  reference: Array<referenceData>;
}

//===========================ksg-map==================================
import { getLevelData } from '@/apis/ksgmap';
import KsgMap from 'ksg-map';
import type { GlobalConfig, OriginalData, AreaData, PointData } from 'ksg-map/dist/types';
import { progressProps } from 'element-plus';

const ksgMap = ref<InstanceType<typeof KsgMap> | null>(null);
const config = ref<GlobalConfig>({
  fullScreen: false,
  showFullScreenButton: false,
  showArea: false,
  maxLevel: Infinity,
  targetArea: 'rootArea',
  allowFocus: false,
  autoLoadLevel: true
});
const data = ref<OriginalData>({
  topAreas: [],
  points: [],
  areas: [],
  focuses: []
});

async function fetchLevelData(ids: string[], type: 'area' | 'focus', level: number, limit: number) {
  return (await getLevelData([klgCode], level, limit)).data;
}
//===========================ksg-map==================================

// 获取知识点详情
// const klgdetail = ref<KlgdetailData>({
//   oid: 0,
//   title: '11111111111',
//   whoName: '2222222222',
//   createTime: '3333333333333',
//   field: '4444444444',
//   sysTitles: '5555555555',
//   notice:
//     ' 对于P型半导体，当电场由半导体表面指向内部，表面势为正值，表面处能带越靠近表面向下弯曲。当空间电荷区能带进一步向下弯曲使得费米能级位置高于禁带中线，意味着表面处出现了一个与衬底导电类型相反的一层，叫做反型层。反型层发生在紧靠在半导体表面处，从反型层到半导体内部之间还夹着一个耗尽层。此时，表面空间电荷区由两部分组成，一部分是耗尽层中的电离受主，另一部分是反型层中的电子，后者堆积在近表面区。\n' +
//     '\n' +
//     '\n' +
//     '\n' +
//     '        对于N型半导体MIS结构，当电场由半导体内部指向表面，表面势为负值，表面处能带越靠近表面向上弯曲。当空间电荷区能带进一步向上弯曲使得费米能级位置低于禁带中线，意味着表面处出现了一个与衬底导电类型相反的一层，叫做反型层。反型层发生在紧靠在半导体表面处，从反型层到半导体内部之间还夹着一个耗尽层。此时，表面空间电荷区由两部分组成，一部分是耗尽层中的电离受主，另一部分是反型层中的电子，后者堆积在近表面区。\n' +
//     '\n' +
//     '这种状态为少数载流子反型状态。',
//   projectList: [
//     {
//       userCoverPic: '',
//       uniqueCode: ''
//     }
//   ],
//   reference: [
//     {
//       id: 0,
//       author: '77777777',
//       bookName: '8888888888',
//       pubPlace: '999999999999',
//       pubHouse: '000000000000000',
//       pubYear: 'aaaaaaaaaa',
//       fileName: 'bbbbbbbbbbb'
//     }
//   ]
// });

// webgl
// const unityContext = new UnityWebgl({
//   loaderUrl: '/Build/WebGL_0304.loader.js',
//   dataUrl: '/Build/WebGL_0304.data',
//   frameworkUrl: '/Build/WebGL_0304.framework.js',
//   codeUrl: '/Build/WebGL_0304.wasm'
// });

// 获取知识点详情
const oid = ref<string>('1');
// 之后以下代码关闭注释，需要从其他页面传递query参数，命名为oid
// onBeforeMount(() => {
//  oid.value = route.query.oid;
// });
const getklgdetail = (oid: string) => {
  getklgdetailApi(oid).then((res) => {
    // console.log('获取详情',res.data.list)
    // klgdetail.value = res.data.list;
  });
};
// getklgdetail(oid.value);

// 前驱知识相关
interface preklgData {
  oid: number;
  title: string;
  sort: string;
}
let currentPage = ref(1);
const limit = 10;
const preklglist = ref<preklgData[]>([]);
const total = ref(0);
let checkedpre = ref(0);
// const getPreklglist = (oid: number, current: number, limit: number) => {
//   getPreklglistApi(oid, current, limit).then((res) => {
//     preklglist.value = res.data.list;
//     total.value = res.data.total;
//     checkedpre.value = res.data.list[0].oid;
//   });
// };

// 初始化
const InitData = () => {
  currentPage.value = 1;
  getPreklglistApi(oid.value, currentPage.value, limit);
};

//InitData();

const handleCurrentChange = (val: number) => {
  getPreklglistApi(oid.value, val, limit);
};

function pickPre(item: any) {
  checkedpre.value = item.oid;
}

// 提交反馈
// const dialogVisible = ref(false);
// const feedbackdata = ref({
//   oid: 0,
//   knowledge_title: '',
//   knowledge_content: ''
// });
// const openFeedback = () => {
//   feedbackdata.value.oid = oid.value;
//   feedbackdata.value.knowledge_title = klgDetail.value.klgTitle;
//   feedbackdata.value.knowledge_content = '';
//   dialogVisible.value = true;
// };
// const saveFeedBack = () => {
//   if (feedbackdata.value.knowledge_content.length >= 500) {
//     ElMessage({
//       type: 'warning',
//       message: '字数太多啦~'
//     });
//   } else {
//     ElMessageBox.confirm('确认提交该反馈吗?', 'Warning', {
//       confirmButtonText: '确认',
//       cancelButtonText: '取消',
//       type: 'warning'
//     }).then(() => {
//       saveFeedBackApi(feedbackdata)
//         .then((res) => {
//           if (res.code == 200) {
//             ElMessage({
//               type: 'success',
//               message: '提交成功'
//             });
//             dialogVisible.value = false;
//           }
//         })
//         .catch((err) => {
//           ElMessage({
//             type: 'info',
//             message: err
//           });
//           dialogVisible.value = false;
//         });
//     });
//   }
// };
</script>
<template>
  <div class="klgdetail-container">
    <template v-if="ready">
      <template v-if="!NoPermisson">
    <div class="klgdetail-innercontainer">
      <div class="navbar" :class="{ folded: isFolded }">
        <div class="nav-change" @click="toggleNavbar">
          <el-icon v-if="isFolded"><Expand /></el-icon>
          <el-icon v-else><Fold /></el-icon>
        </div>
        <div class="nav-items" :class="{ folded: isFolded }">
          <div
            @click="setActive('knowledge')"
            :class="['nav-item', { active: currentActive == 'knowledge' }]"
          >
            <el-icon><Document /></el-icon>
            <span v-if="!isFolded">知识内容</span>
            <el-icon v-if="!isFolded" style="margin-left: 28px"><ArrowRightBold /></el-icon>
          </div>
          <div @click="setActive('map')" :class="['nav-item', { active: currentActive == 'map' }]">
            <el-icon><Orange /></el-icon>
            <span v-if="!isFolded">知识源图</span>
            <el-icon v-if="!isFolded" style="margin-left: 28px"><ArrowRightBold /></el-icon>
          </div>
          <div
            @click="setActive('evaluation')"
            :class="['nav-item', { active: currentActive == 'evaluation' }]"
          >
            <el-icon><EditPen /></el-icon>
            <span v-if="!isFolded">讲解测评</span>
            <el-icon v-if="!isFolded" style="margin-left: 28px"><ArrowRightBold /></el-icon>
          </div>
          <div
            @click="setActive('application')"
            :class="['nav-item', { active: currentActive == 'application' }]"
          >
            <el-icon><Opportunity /></el-icon>
            <span v-if="!isFolded">应用场景</span>
            <el-icon v-if="!isFolded" style="margin-left: 28px"><ArrowRightBold /></el-icon>
          </div>
        </div>
      </div>
      <div class="content-container" v-if="klgDetail.klgTitle">
        <div class="klgdetail-header">
          <!-- 左侧：知识点名称和标签 -->
          <div class="klgdetail-header-left">
            <div class="klgdetail-title-row">
              <span class="klgdetail-name" v-html="klgDetail.klgTitle"></span>
              <el-tooltip
                :content="tooltipContent"
                :disabled="klgDetail.status !== '未学习' && klgDetail.status !== '已掌握'"
                placement="top"
              >
                <span v-if="klgDetail.status" class="klgdetail-status">{{
                  klgDetail.status
                }}</span>
              </el-tooltip>
            </div>
            <div class="klgdetail-tags">
              <span v-for="tag in klgDetail.tags" :key="tag" class="klgdetail-tag">{{ tag }}</span>
            </div>
          </div>
          <!-- 右侧：进度条和统计 -->
          <div class="klgdetail-header-right">
            <div class="klg-progress-card">
              <!-- 列1：图标 -->
              <div class="klg-progress-col icon-col">
                <el-icon><Document /></el-icon>
              </div>
              <!-- 列2：未学习 -->
              <div
                class="klg-progress-col stage-col"
                :class="{ 'gray-col': klgDetail.status !== '未学习' }"
              >
                未学习
              </div>
              <!-- 列3：完成测评（竖排三行） -->
              <div class="klg-progress-col triple-col">
                <div class="triple-title">完成测评</div>
                <div class="triple-bar">
                  <!-- <span class="klg-progress-dot"></span>
                  <span class="klg-progress-line"></span> -->
                  <el-progress :percentage="klgDetail.mastered / klgDetail.total * 100" :show-text="false" :stroke-width="3" />
                </div>
                <div class="triple-value">{{ klgDetail.mastered }}/{{ klgDetail.total }}</div>
              </div>
              <!-- 列4：已掌握 -->
              <div
              class="klg-progress-col stage-col"
              :class="{ 'gray-col': klgDetail.status !== '已掌握' }"
              >
                已掌握
              </div>
              <!-- 列5：完成前驱知识学习（竖排三行） -->
              <div class="klg-progress-col triple-col">
                <div class="triple-title">完成前驱知识学习</div>
                <div class="triple-bar">
                  <!-- <span class="klg-progress-dot"></span>
                  <span class="klg-progress-line"></span> -->
                  <el-progress :percentage="klgDetail.preLearned / klgDetail.preTotal" :show-text="false" :stroke-width="3" />
                </div>
                <div class="triple-value">{{ klgDetail.preLearned }}/{{ klgDetail.preTotal }}</div>
              </div>
              <!-- 列6：全掌握 -->
              <div
                class="klg-progress-col stage-col"
                :class="{ 'gray-col': klgDetail.status !== '全掌握' }"
              >
                全掌握
              </div>
            </div>
          </div>
        </div>

        <div class="klgdetail-container-content">
          <!-- 动态内容区域 -->
          <component
            :is="currentContentComponent.component"
            v-bind="currentContentComponent.props"
          ></component>
        </div>
      </div>

      <!-- <el-dialog v-model="dialogVisible" width="526px" :z-index="10">
        <template #header>
          <div class="dialog-header">我要反馈</div>
        </template>
        <div class="dialog-title" v-html="klgDetail.klgTitle"></div>
        <div class="dialog-feedback-title">反馈内容</div>

        <div class="dialog-feedback-content">
          <CkEditorClassic></CkEditorClassic>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <CmpButton type="info" @click="dialogVisible = false" class="dialog-button"
              >关闭窗口</CmpButton
            >
            <CmpButton type="primary" @click="saveFeedBack" class="dialog-button"
              >提交反馈</CmpButton
            >
          </span>
        </template>
      </el-dialog> -->
    </div>
        </template>
        <ErrorDialog v-else :errorMessage="errorInfo"></ErrorDialog>
      </template>
  </div>
</template>

<style scoped lang="less">
// .dialog-footer {
//   display: flex;
//   justify-content: center;
// }

// .dialog-button {
//   width: 160px;
//   height: 43.2px;
//   font-size: 14px;
//   margin-right: 21px;
// }

.klgdetail-container {
  margin-top: 20px;
  display: flex;
  //align-items: center;
  justify-content: center;
  //max-width: 1220px;

  .klgdetail-innercontainer {
    width: 1400px;
    display: flex;

    .navbar {
      display: flex;
      flex-direction: column;
      //background-color: #f4f4f4;
      padding: 10px;
      flex: 0 0 160px;
      //justify-content: center;
      margin-top: 64px;

      .nav-change {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        .el-icon {
          font-size: 25px;
          color: #333333;
        }
      }

      .nav-item {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        height: 60px;
        transition:
          background-color 0.3s,
          border-radius 0.3s;
        color: #333333;
        margin-bottom: 12px;

        .el-icon {
          margin-left: 5px;
          margin-right: 5px;

          &:hover {
            //transform: scale(1.2); // 鼠标悬停时放大
          }
        }
      }

      .nav-item:hover {
        background-color: #f2f2f2;
        border-radius: 5px;
      }

      .nav-item.active {
        background-color: #f2f2f2;
        border-radius: 5px;
      }
    }
    .navbar.folded {
      flex: 0 0 70px;
    }
    .nav-item span {
      display: inline; // 默认显示
    }

    .navbar.folded .nav-item span {
      display: none; // 折叠时隐藏文字
    }

    .content-container {
      display: flex;
      flex-direction: column;
      margin-left: 10px;

      .klgdetail-header {
        min-width: 1200px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        //padding: 12px 16px;
        padding-bottom: 2px;
        //padding-top: 12px;
        padding-left: 16px;
        //padding-right: 0px;
        background: #fff;
        //border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.03);
        //maxWidth: 1220px;
        max-width: 1200px;

        border-width: 1px;
        border-style: solid;
        border-color: rgba(220, 223, 230, 1);
        border-top: none;
        border-left: none;
        border-right: none;
      }

      .klgdetail-header-left {
        flex: 1;
      }

      .klgdetail-title-row {
        display: flex;
        align-items: center;
      }

      .klgdetail-name {
        font-size: 18px;
        font-weight: bold;
        margin-right: 12px;
      }

      .klgdetail-status {
        background: #fff3e0;
        color: #ff9800;
        border-radius: 4px;
        padding: 2px 8px;
        font-size: 12px;
        margin-left: 4px;
      }

      .klgdetail-tags {
        margin-top: 8px;
      }

      .klgdetail-tag {
        display: inline-block;
        background: #f2f2f2;
        color: #888;
        border-radius: 12px;
        padding: 2px 12px;
        font-size: 12px;
        margin-right: 8px;
        margin-bottom: 4px;
      }

      .klgdetail-header-right {
        min-width: 260px;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
      }

      .klg-progress-card {
        display: flex;
        align-items: center;
        border: 1px solid rgba(220, 223, 230, 1);
        border-radius: 8px;
        background: #fff;
        //padding: 0 16px;
        //min-width: 420px;
        max-width: 400px;
        height: 54px; /* 扁平感 */
        font-size: 14px;
        box-sizing: border-box;
        gap: 0;
      }

      .klg-progress-col {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 60px;
        height: 100%;
        text-align: center;
      }

      .icon-col {
        min-width: 32px;
        color: #bfcbd9;
        font-size: 20px;
      }

      .stage-col {
        min-width: 60px;
        color: #222;
        font-weight: bold;
        font-size: 15px;
      }

      .gray-col {
        color: #bbb;
        font-weight: normal;
      }

      .triple-col {
        min-width: 90px;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        padding: 0 4px;
      }

      .triple-title {
        font-size: 11px;
        color: #888;
        margin-bottom: 0;
        line-height: 1.1;
      }

      .triple-bar {
        display: flex;
        align-items: center;
        width: 60px;
        margin: 2px 0;

        .el-progress--line {
          width: 60px;
          .el-progress__text {
            width: 25px;
          }
        }
      }

      .klg-progress-dot {
        width: 7px;
        height: 7px;
        background: #409eff;
        border-radius: 50%;
        display: inline-block;
        margin-right: 2px;
      }

      .klg-progress-line {
        flex: 1;
        height: 2px;
        background: #e4e7ed;
        border-radius: 1px;
      }

      .triple-value {
        font-size: 11px;
        color: #222;
        font-weight: 500;
        margin-top: 0;
        line-height: 1.1;
      }
    }
  }
}

.pagination-block {
  display: flex;
  width: 100%;
  flex-direction: row;
  justify-content: center;
  margin-top: 5px;
}
</style>
