<template>
  <template v-if="!showWrongSets">
    <template v-if="!showDraftQuestionSubmit">
      <div class="qt-detail">
        <!-- 头部 -->
        <div class="header">
          <div class="header-left">
            <CmpButton class="back-btn" type="primary" @click="backhome">返回</CmpButton>
            <h1>{{ projectStore.info.title }}</h1>
          </div>
          <!-- <div class="process">测试完成度：{{ currentCount }} / {{ total }}</div> -->
          <div class="process">测试完成度：{{ current - 1 }} / {{ total }}</div>
          <!-- FIXME：currentCount还不如current-1 -->
        </div>
        <!-- 做题 -->
        <div class="main-content">
          <div class="title">{{ currentTest.sectionTitle }}</div>
          <div class="content">
            <div class="content-question">
              ({{ currentTest.order }}).{{ currentTest.examTitle }}
            </div>
            <div v-if="currentTest.examType == ExamType.fillBlank" class="replycontent-style">
              <p class="replycontent-title">回答:</p>
              <ClassicEditor v-model="myAnswer"></ClassicEditor>
            </div>
            <div v-if="currentTest!.examType == ExamType.choice">
              <el-form-item label="">
                <el-radio-group v-model="myAnswer" class="selection-style">
                  <el-radio
                    :label="indexIntoAlpha(index)"
                    v-for="(selection, index) in currentTest.examChoices"
                    :key="index"
                    ><span>{{ indexIntoAlpha(index) }}</span
                    >{{ selection }}</el-radio
                  >
                </el-radio-group>
              </el-form-item>
            </div>
            <div v-if="currentTest.examType == ExamType.judgment">
              <el-form-item label="">
                <el-radio-group v-model="myAnswer" class="selection-style">
                  <el-radio label="1"><span class="iconfont icon-duigou1"></span></el-radio>
                  <el-radio label="2"><span class="iconfont icon-cuowu"></span></el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <div v-if="currentTest.examType == ExamType.shortAnswer" class="replycontent-style">
              <p class="replycontent-title">回答:</p>
              <ClassicEditor v-model="myAnswer"></ClassicEditor>
            </div>
          </div>
        </div>
        <!-- 上一题--查看答案--下一题按钮 -->
        <div class="btns" style="margin-top: 20px">
          <CmpButton class="btn" type="info" v-if="current > 1" @click="previousQs"
            >上一题</CmpButton
          >
          <CmpButton class="btn" type="info" @click="checkAnswer">查看答案</CmpButton>
          <CmpButton class="btn" type="primary" v-if="current != total" @click="nextQs"
            >下一题</CmpButton
          >
          <CmpButton class="btn" type="primary" v-if="current == total" @click="finish"
            >完成</CmpButton
          >
        </div>
        <!-- 查看答案 -->
        <div class="answer-wrap" v-if="answerShow">
          <div class="answer">
            <div v-if="isRight == 0" class="msg correct-color">
              <img
                style="width: 14px; height: 14px"
                src="@/assets/images/prjlearn/correct.png"
                alt=""
              />
              恭喜你，答对了！
            </div>
            <div v-if="isRight == 1" class="msg error-color">
              <img
                style="width: 14px; height: 14px"
                src="@/assets/images/prjlearn/error.png"
                alt=""
              />
              很遗憾，您的答案不正确。
            </div>
            <div class="detail">
              <div class="choice">
                <div v-if="currentTest.examType == ExamType.judgment">
                  答案：{{ currentTest.examAnswer == '1' ? '正确' : '错误' }}
                </div>
                <div v-else>答案：{{ currentTest.examAnswer }}</div>
                <div
                  style="color: var(--color-theme-project); font-weight: 700; cursor: pointer"
                  @click="buildupQuestion(currentTest)"
                >
                  提问
                </div>
              </div>
              <div class="description">
                说明：
                <div>{{ currentTest.examExplanation }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template v-else>
      <QuestionSubmit
        @back="showDraftQuestionSubmit = !showDraftQuestionSubmit"
        :questionObj="questionObj"
      ></QuestionSubmit>
    </template>
  </template>
  <template v-else="showWrongSets">
    <template v-if="!showWrongSetsQuestionSubmit">
      <WrongSets :wrongSets="wrongSets" @buildupQuestion="buildupWrongSetsQuestion"></WrongSets>
    </template>
    <template v-else>
      <QuestionSubmit
        @back="showWrongSetsQuestionSubmit = !showWrongSetsQuestionSubmit"
        :questionObj="questionObj"
      ></QuestionSubmit>
    </template>
  </template>
</template>

<script setup lang="ts">
import QuestionSubmit from './QuestionSubmit.vue';
import ClassicEditor from '@/components/editors/Vditor.vue';

// import CkEditorInline from '@/components/CkEditor/CkEditorInline.vue';
import CmpButton from '@/components/CmpButton.vue';
import WrongSets from './WrongSets.vue';
import { ShowType } from '@/types/exam';
import indexIntoAlpha from '@/utils/indexIntoAlpha';
import type { documentData } from '@/types/data';
import { ExamType } from '@/types/exam';

import { getDocumentListApi, saveAnswerApi } from '@/apis/exam';
import userBehaviour from '@/utils/userBehaviour';
import { PrjForm } from '@/types/project';
import { finishTestApi } from '@/apis/exam';
import { useProjectStore } from '@/stores/project';

const projectStore = useProjectStore();

const { initUserBehaviour } = userBehaviour(PrjForm.draft);

const showType = inject('showType') as Ref<ShowType>;
const showWrongSets = ref(false);
const showDraftQuestionSubmit = ref(false);
const showWrongSetsQuestionSubmit = ref(false);
const router = useRouter();
const route = useRoute();
const spuId = route.query.spuId as string;
const current = ref(1); //当前在第几题,初始化的值从其他页面传递，为当前进度，给后端发信（前端来操作数据
// const currentCount = ref(0); //完成度，展示后端数据
const total = ref(1); //题目总数
// 当前题目
let currentTest = ref<documentData>({
  spuId: '1',
  examId: 0, //测试题目id
  sectionId: 0, //小节id
  order: 0, //题目序号
  sectionTitle: '', //小节名称
  examType: 0, //1.填空 2选择 3判断 4问答
  examTitle: '', //题目
  examChoices: [''], //选项
  examAnswer: '', //答案
  myAnswer: '',
  examExplanation: '', //解释说明
  questionList: [
    {
      questionId: 0, //问题自增id
      associatedWords: '', //关联文本内容
      keyword: '', //关键字内容
      questionType: '', //问题类型，是什么、为什么、怎么做
      questionNecessity: 0, //问题必要性 1必须问题，2参考问题
      creatorId: '', //创建者id
      creatorName: '', //创建者名字
      createTime: '', //创建时间
      explanation: '',
      answerList: [
        {
          answerId: 0, //回答自增id
          answerKlgs: [], //知识点名称列表
          answerExplanation: '', //解释
          createTime: '', //回答时间
          creatorName: '', //回答者名称
          modifiedTime: '' //修改时间
        }
      ]
    }
  ]
});
// 获取初始数据，当从其他页面(视频介绍页）点击开始测评，需要传递当前进度

//waqtodo:这个接口需要调整，如果是测评类项目就只传当前项目的spuId，
//如果是某一个案例下的测评，就需要传两个参数，分别是父id(目前计划从)/project/test/index,通过路由传参的方式获取其父项目Id
const getDocumentList = async () => {
  const params = {
    current: current.value,
    limit: 1,
    spuId
  };
  try {
    const res = await getDocumentListApi(params);
    myAnswer.value = res.data.map.myAnswer;
    total.value = res.data.total;
    currentTest.value = res.data.map;
    router.replace({
      query: {
        ...route.query,
        chapterId: res.data.map.sectionId
      }
    });
  } catch (err) {
    ElMessage({
      message: err as string,
      type: 'warning'
    });
    current.value -= 1;
  }
};

onMounted(async () => {
  await getDocumentList();
  initUserBehaviour(null);
  // Title.value = '微积分函数测验'; //后续通过跳转页面的时候传值获得
});
// 保存答案
const myAnswer = ref('');
async function saveAnswer() {
  const params = {
    order: currentTest.value.order,
    examId: currentTest.value.examId,
    myAnswer: myAnswer.value,
    spuId: currentTest.value.spuId,
    examAnswer: currentTest.value.examAnswer
  };
  await saveAnswerApi(params);
}
// 点击上一题
const previousQs = () => {
  answerShow.value = false;
  current.value -= 1;
  // // 向后端请求
  getDocumentList();
};
// 点击下一题,先保存答案，保存成功才进入下一题
const nextQs = () => {
  // 保存答案并获取下一题目
  if (myAnswer.value) {
    saveAnswer()
      .then(() => {
        answerShow.value = false;
        current.value += 1;
        // myAnswer.value = '';
      })
      .then(() => {
        getDocumentList();
      });
  } else {
    ElMessage({
      message: '请完成这道题目',
      type: 'warning'
    });
  }
};
const isRight = ref(0); // 0表示显示正确，1表示显示错误，2表示都不显示
const answerShow = ref(false); //显示答案
// 查看答案
const checkAnswer = () => {
  answerShow.value = true;
  if (
    (currentTest.value.examType == ExamType.choice ||
      currentTest.value.examType == ExamType.judgment) &&
    myAnswer.value == currentTest.value.examAnswer
  ) {
    isRight.value = 0;
  } else if (
    (currentTest.value.examType == ExamType.choice ||
      currentTest.value.examType == ExamType.judgment) &&
    myAnswer.value != currentTest.value.examAnswer
  ) {
    isRight.value = 1;
  } else {
    isRight.value = 2;
  }
};

// 完成测评
const wrongSets = ref({
  Title: '这是一个默认的标题',
  currentCount: 1,
  accuracy: '',
  list: Array<documentData>,
  total: 0
});
const finish = async () => {
  await saveAnswer();
  const res = await finishTestApi({
    spuId
  });
  wrongSets.value.Title = projectStore.info.title;
  wrongSets.value.currentCount = total.value;
  wrongSets.value.accuracy = res.data.accuracy;
  wrongSets.value.list = res.data.list;
  showWrongSets.value = true;
};

// 提问
const emit = defineEmits(['handleoffSectionId']);
const questionObj = ref<documentData>();
const buildupQuestion = (item: documentData) => {
  questionObj.value = item;
  showDraftQuestionSubmit.value = true;
};

const buildupWrongSetsQuestion = (obj: documentData) => {
  questionObj.value = obj;
  showWrongSetsQuestionSubmit.value = true;
};

// 返回
const backhome = () => {
  ElMessageBox.confirm('确定要退出练习？退出后未完成的练习会保存在练习历史中', 'Warning', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      showType.value = ShowType.map;
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消'
      });
    });
};
</script>

<style lang="less" scoped>
// 滑动效果
.qt-detail {
  width: 1100px;
  padding: 8px 23px 20px 10px;

  .header {
    width: 100%;
    height: 60px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f2f2f2;

    .header-left {
      display: flex;
      flex-direction: row;
      height: 100%;
      align-items: center;

      .back-btn {
        height: 56px;
        width: 80px;
      }

      h1 {
        margin-left: 16px;
        font-size: 20px;
        font-weight: 700;
      }
    }

    .process {
      font-size: 14px;
      color: var(--color-theme-project);
    }
  }

  .main-content {
    margin-top: 15px;
    width: 100%;

    .title {
      width: 100%;
      height: 33px;
      line-height: 33px;
      font-size: 14px;
      font-weight: 400;
      background-color: #f2f2f2;
      padding-left: 10px;
    }

    .content {
      padding-left: 62px;
      min-height: 300px;
      margin-top: 22px;

      .content-question {
        margin-left: 52px;
        display: flex;
        flex-wrap: wrap;
        white-space: pre-wrap;
        word-wrap: break-word;
        font-size: 14px;
        font-weight: 700;
      }

      .selection-style {
        display: flex;
        flex-direction: column;
        margin-top: 22px;
        align-items: flex-start;

        .el-radio {
          margin-bottom: 29px;

          span {
            margin-left: 32px;
            margin-right: 5px;
          }
        }
      }

      .replycontent-style {
        margin-top: 31px;
        margin-left: 32px;
        font-size: 14px;

        .replycontent-title {
          margin-bottom: 10px;
        }
      }
    }
  }

  .finish-content {
    margin-top: 15px;
    width: 970px;
    height: 80px;
    background-color: #f0f9eb;
    margin-left: 40px;
    display: flex;
    flex-direction: row;
    align-items: center;

    .finish-accuracy {
      margin-left: 40px;
    }

    .btns2 {
      display: flex;
      flex-direction: row;
      justify-content: center;
      margin-left: 369px;

      .btn {
        width: 160px;
        height: 44px;
        margin-left: 20px;
      }
    }
  }

  .btns {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;

    .btn {
      width: 160px;
      height: 44px;
      margin-left: 20px;
    }
  }

  .answer-wrap {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 25px;

    .answer {
      width: 92%;

      .msg {
        height: 37px;
        display: flex;
        align-items: center;

        width: 100%;

        img {
          margin-left: 7px;
          margin-right: 7px;
        }
      }

      .correct-color {
        color: #67c23a;
        background-color: #f0f9eb;
      }

      .error-color {
        background-color: #fdf6ec;
        color: #e6a23c;
      }

      .detail {
        width: 100%;
        border: 1px solid #f2f2f2;
        border-radius: 4px;
        margin-top: 4px;
        padding-left: 25px;
        padding-right: 16px;
        font-size: 14px;

        .choice {
          display: flex;
          flex-direction: row;
          width: 100%;
          justify-content: space-between;
        }

        .description {
          margin-top: 20px;
          white-space: pre-wrap;
        }
      }
    }
  }

  .error-wrap {
    margin-top: 19px;
    margin-left: 40px;
    width: 970px;
    height: 472px;
    background-color: #ffffff;
    border: 0.8px solid rgb(242, 242, 242);
    border-radius: 5px;
    box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;

    .error-innerwrap {
      padding-top: 15px;

      .error-top {
        display: flex;
        flex-direction: row;

        .error-top-content {
          padding-right: 50px;
          width: 527px;

          .content-question {
            font-size: 14px;
            font-weight: 700;
            color: #333333;
            margin-left: 74px;
          }

          .replycontent-title {
            margin-left: 30px;
          }

          .replycontent {
            width: 447px;
            margin: 10px 10px 10px 30px;
          }

          .selection-style {
            display: flex;
            flex-direction: column;
            margin-left: 30px;
            align-items: flex-start;

            .el-radio {
              margin-top: 18px;

              span {
                margin-left: 32px;
                margin-right: 5px;
              }
            }
          }

          .explanation-wrap {
            width: 447px;
            min-height: 134px;
            background-color: #ffffff;
            border: 0.8px solid rgb(242, 242, 242);
            border-radius: 5px;
            margin-left: 30px;
            padding: 11px 22px;
            font-size: 14px;

            .detail {
              white-space: pre-wrap;

              .choice {
                display: flex;
                flex-direction: row;
                justify-content: space-between;
              }

              .description {
                margin-top: 20px;
              }
            }
          }
        }

        .error-top-list {
          width: 422px;
          height: 355px;
          background-color: rgba(242, 242, 242, 0.376);
          border-radius: 5px;
          padding: 15px 22px;
          overflow-y: auto;

          p {
            font-size: 14px;
            font-weight: 700;
          }

          .hover-style:hover {
            font-weight: 700;
            color: #005579;
            cursor: pointer;
          }

          .questionliststyle {
            margin-top: 17px;
            font-size: 14px;
          }
        }
      }

      .error-bottom {
        margin-top: 18px;
        margin-left: 30px;
        margin-bottom: 40px;

        .btn {
          width: 160px;
          height: 44px;
        }
      }
    }
  }
}
</style>
