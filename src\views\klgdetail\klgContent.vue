<template>
  <div class="content-container" v-if="ready">
    <div class="klgdetail-container-content">
      <div class="klgdetail-and-reference">
        <div style="display: flex; align-items: center; justify-content: space-between; padding-top: 10px; padding-bottom: 10px;">
          <span style="background-color: rgba(25, 115, 203, 1); width:8px; height: 15px; display:inline-block; margin-right: 5px;"></span>
          <span class="preklg-title-left">基本内容</span>
          <el-tooltip content="如果您对该知识的内容有不同看法，欢迎给我们留言反馈您的想法。" placement="top">
            <span @click="openFeedback" class="hoverStyle" style="margin-left:auto; cursor:pointer;">
              <i class="iconfont icon-xiugai" style="font-size:13px; vertical-align: middle;"></i>
              <span style="font-size: 13px; margin-left: 5px;">反馈</span>
            </span>
          </el-tooltip>
        </div>
        <div class="klgdetail">
          <div
            class="klgdetail-video-list"
            v-if="props.klgDetail.projectList && props.klgDetail.projectList.length > 0"
          >
            <div
              class="klgdetail-video-item"
              v-for="item in props.klgDetail.projectList"
              :key="item.uniqueCode"
            >
              <template v-if="item.userCoverPic && item.uniqueCode">
                <img :src="item.userCoverPic" style="width: 128px; height: 67px" />
              </template>
            </div>
          </div>
          <div class="klgdetail-content">
            <div
              class="klgdetail-synonym"
              v-if="props.klgDetail.sysTitles && props.klgDetail.sysTitles.length > 0"
            >
              同义词:{{ props.klgDetail.sysTitles }}
            </div>
            <p
              class="klgdetail-content-p"
              v-html="processAllLatexEquations(props.klgDetail.notice)"
            ></p>
          </div>
        </div>
      </div>
      <div class="preklg-container">
        <div class="preklg">
          <div class="preklg-title" style="padding-top: 10px; padding-bottom: 10px; padding-right: 8px;">
            <span style="background-color: rgba(25, 115, 203, 1); width:8px; height: 15px; display:inline-block; margin-right: 5px;"></span>
            <span class="preklg-title-left">前驱知识</span>
            <span class="preklg-title-right">（全掌握/总前驱知识数：0 / {{ total }}）</span>
          </div>
          <div class="reference-list-item" style="text-align: center">
            <span style="text-align: center" v-if="total == 0">暂无数据</span>
          </div>
          <div class="preklg-list">
            <div
              class="preklg-list-item"
              v-for="item in preklglist"
              :key="item.klgCode"
              :class="item.klgCode == checkedpre ? 'defaultClass' : 'defaultClass'"
              @click="pickPre(item)"
            >
              <span class="item1" v-html="item.title"></span>
              <span class="item2"
                ><template v-if="item.sortId == '1'">未学习</template>
                <template v-else-if="item.sortId == '2'">已掌握</template>
                <template v-else-if="item.sortId == '3'">全掌握</template>
                <template v-else>自定义类型</template></span
              >
            </div>
            <div class="pagination-block">
              <!-- <el-pagination background layout="prev, pager, next" :total="total" /> -->
              <el-pagination
                v-model:current-page="currentPage"
                :page-size="limit"
                small
                background
                layout="prev, pager, next"
                :total="total"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-dialog v-model="dialogVisible" width="526px" :z-index="10">
      <template #header>
        <div class="dialog-header">我要反馈</div>
      </template>
      <div class="dialog-title" v-html="klgDetail.klgTitle"></div>
      <div class="dialog-feedback-title">反馈内容</div>

      <div class="dialog-feedback-content">
        <ClassicEditor v-model="feedbackdata.knowledge_content"></ClassicEditor>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <CmpButton type="info" @click="dialogVisible = false" class="dialog-button"
            >关闭窗口</CmpButton
          >
          <CmpButton type="primary" @click="saveFeedBack" class="dialog-button">提交反馈</CmpButton>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
// import CkEditorClassic from '@/components/CkEditor/CkEditorClassic.vue';
import ClassicEditor from '@/components/editors/Vditor.vue';
import { defineProps } from 'vue';
import KsgMap from 'ksg-map';
import type { GlobalConfig, OriginalData, AreaData, PointData } from 'ksg-map/dist/types';
import { getLevelData } from '@/apis/ksgmap';
import { getklgdetailApi, saveFeedBackApi, getPreklglistApi } from '@/apis/klgdetail';
import { ElSwitch } from 'element-plus';
import { processAllLatexEquations } from '@/utils/latexUtils';

const ksgMap = ref<InstanceType<typeof KsgMap> | null>(null);
const config = ref<GlobalConfig>({
  fullScreen: false,
  showFullScreenButton: false,
  showArea: false,
  maxLevel: Infinity,
  targetArea: 'rootArea',
  allowFocus: false,
  autoLoadLevel: true
});

const props = defineProps<{
  klgDetail: {
    title: string;
    description: string;
    klgTitle: string;
    provider: string;
    createTime: string;
    field: string;
    sysTitles: string;
    notice: string;
    projectList: [{ userCoverPic: string; uniqueCode: string }];
    reference: [
      {
        refid: number;
        cntName: string;
        indexPage: string;
      }
    ];
  };
  klgCode: string;
}>();

// 前驱知识相关
interface preklgData {
  klgCode: string;
  title: string;
  sortId: string;
}
let currentPage = ref(1);
const limit = 10;
const total = ref(0);
const preklglist = ref<preklgData[]>([]);
let checkedpre = ref('0');

const getPreklglist = async (current: number, limit: number) => {
  if (!props.klgCode) {
    return;
  }
  try {
    const res = await getPreklglistApi(props.klgCode, current, limit);
    const res1 = res;
    preklglist.value = (res1.data.records || []).map((item: any) => ({
      klgCode: item.klgCode,
      title: item.title,
      sortId: item.status === 0 ? '1' : item.status === 1 ? '2' : item.status === 2 ? '3' : '0'
    }));
    total.value = res1.data.total;
    if (res1.data.records && res1.data.records.length > 0) {
      checkedpre.value = res1.data.records[0].klgCode;
    }
  } catch (error) {
    preklglist.value = [];
    total.value = 0;
    checkedpre.value = '';
    console.error('Error fetching preklg list:', error);
  }
};

import { useRouter } from 'vue-router';
const router = useRouter();
function pickPre(item: any) {
  checkedpre.value = item.klgCode;
  const { href } = router.resolve({
    path: '/klgdetail',
    query: {
      klgCode: item.klgCode
    }
  });
  window.open(href, '_blank');
}
const handleCurrentChange = async (val: number) => {
  await getPreklglist(val, limit);
};
// 初始化
const InitData = async () => {
  if (!props.klgCode) {
    console.warn('klgCode is not available, skipping InitData');
    return;
  }
  currentPage.value = 1;
  await getPreklglist(currentPage.value, limit);
};

const ready = ref(false);

// 延迟初始化，等待props可用
onMounted(async () => {
  ready.value = false;
  if (props.klgCode) {
    await InitData(); // 等待数据加载完毕
  }
  ready.value = true;
});
const data = ref<OriginalData>({
  topAreas: [],
  points: [],
  areas: [],
  focuses: []
});
// onMounted已在上面定义，这里移除重复的调用
async function fetchLevelData(ids: string[], type: 'area' | 'focus', level: number, limit: number) {
  if (!props.klgCode) {
    console.error('klgCode is not available for fetchLevelData');
    return [];
  }
  return (await getLevelData([props.klgCode], level, limit)).data;
}

// 提交反馈
const dialogVisible = ref(false);
const feedbackdata = ref({
  klgCode: '',
  knowledge_title: '',
  knowledge_content: ''
});
const openFeedback = () => {
  if (!props.klgCode || !props.klgDetail) {
    console.error('klgCode or klgDetail is not available');
    return;
  }
  feedbackdata.value.klgCode = props.klgCode;
  feedbackdata.value.knowledge_title = props.klgDetail.klgTitle;
  feedbackdata.value.knowledge_content = '';
  dialogVisible.value = true;
};
const saveFeedBack = () => {
  if (feedbackdata.value.knowledge_content.length >= 500) {
    ElMessage({
      type: 'warning',
      message: '字数太多啦~'
    });
  } else {
    ElMessageBox.confirm('确认提交该反馈吗?', 'Warning', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      const submitData = {
        klgCode: feedbackdata.value.klgCode,
        content: feedbackdata.value.knowledge_content,
      }
      saveFeedBackApi(submitData)
        .then((res) => {
          if (res.code == 0) {
            ElMessage({
              type: 'success',
              message: '提交成功'
            });
            dialogVisible.value = false;
          }
        })
        .catch((err) => {
          ElMessage({
            type: 'info',
            message: err
          });
          dialogVisible.value = false;
        });
    });
  }
};
</script>

<style scoped>
.content-container {
  display: flex;
  flex-direction: column;
  margin-left: 10px;
  .klgdetail-container-content {
    display: flex;
    flex-direction: row;

    .klgdetail-and-reference {
      margin-right: 10px;
      width: 738px;

      .preklg-title-left {
          font-family: '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif !important;
          font-weight: 600 !important;
          font-style: normal !important;
          font-size: 16px !important;
        }

      .klgdetail {
        .klgdetail-name {
          font-size: 18px;
          font-weight: 700;
          margin-bottom: 9px;
        }

        .klgdetail-more {
          font-size: 14px;
          font-weight: 400;
          color: #797979;

          :is(span) {
            margin-right: 83px;
          }
        }

        .klgdetail-video {
          margin-top: 5px;
          display: flex;
          flex-direction: row;

          .klgdetail-video-item {
            width: 128px;
            height: 67px;
            margin-right: 10px;
          }
        }

        .klgdetail-content {
          margin-top: 10px;
          word-break: break-all;
          /* padding: 10px; */
          min-height: 322px;
          /* border: 1px solid #eee; */

          .klgdetail-synonym {
            font-size: 12px;
            font-weight: 400;
            margin-bottom: 13px;
            color: #333333;
          }

          .klgdetail-content-p {
            white-space: pre-wrap;
            text-indent: 2em;
            font-size: 14px;
            font-family: '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;
            font-weight: 400;
            font-style: normal;
            text-align: left;
          }
        }
      }

      .reference {
        margin-top: 20px;

        .reference-title {
          font-size: 16px;
          font-weight: 700;
          color: #333333;
          margin-bottom: 19px;
        }

        .reference-list {
          .reference-list-item {
            width: 781px;
            height: 33px;
            /* background-color: rgba(242, 242, 242, 0.996); */
            font-size: 14px;
            font-weight: 400;
            line-height: 33px;
            margin-bottom: 5px;
            padding-left: 10px;

            :is(span) {
              margin-right: 10px;
            }
          }
        }
      }
    }

    .preklg-container {
      flex: 1;
      max-width: 400px;

      .preklg {
        .preklg-title-left {
          font-family: '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif !important;
          font-weight: 600 !important;
          font-style: normal !important;
          font-size: 16px !important;
        }

        .preklg-title-right {
          font-family: '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif !important;
          font-weight: 400 !important;
          font-style: normal !important;
          font-size: 12px !important;
          color: #666666 !important;
        }

        .preklg-list {
          width: 453px;

          .clickedClass {
            background-color: #1973cb;
            color: #fff;
          }

          .defaultClass {
            background-color: #ffff;
          }

          .preklg-list-item {
            height: 32px;
            border: 1px solid rgba(220, 223, 230, 1);
            margin-top: 6px;
            line-height: 32px;
            text-indent: 5px;
            display: flex;
            justify-content: space-between;
            padding-right: 15px;
            font-size: 14px;
            //font-weight: 600;
            //font-kerning: normal;
            align-items: center;

            .item1 {
              font-size: 14px;
              font-weight: 400;
              //font-style: normal;
              //font-kerning: normal;
              font-family: '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
              font-style: normal;
            }

            .item2 {
              background: #f1f1f1;
              margin-left: 0px;
              display: flex;
              align-items: center;
              max-height: 20px;
              font-family: '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
              font-weight: 400;
              font-style: normal;
              font-size: 12px;
              color: #666666;
            }
          }

          .pagination-block {
            display: flex;
            width: 100%;
            flex-direction: row;
            justify-content: center;
            margin-top: 10px;
          }
        }
      }
    }
  }

  .klgdetail-container-feedback {
    width: 100%;
    height: 46px;
    background-color: rgba(0, 85, 121, 0.13);
    background-color: #d6e9f6;
    line-height: 46px;
    text-indent: 10px;
    color: #1973cb;
    margin-top: 10px;
    margin-bottom: 20px;

    .feedback-style1 {
      font-size: 18px;
      font-weight: bold;
    }

    .feedback-style2 {
      font-size: 12px;
    }

    .hoverStyle:hover {
      cursor: pointer;
    }
  }

  .el-dialog {
    .dialog-header {
      font-size: 18px;
      font-weight: 700;
      color: #1973cb;
    }

    .dialog-feedback-content {
      width: 480px;
      height: 337px;
      margin-top: 8px;
    }

    .dialog-title {
      font-size: 16px;
      font-weight: 700;
      color: #333333;
      margin-bottom: 18px;
    }
    .dialog-footer {
      display: flex;
      justify-content: center;
    }

    .dialog-button {
      width: 160px;
      height: 43.2px;
      font-size: 14px;
      margin-right: 21px;
    }
  }
}
</style>
