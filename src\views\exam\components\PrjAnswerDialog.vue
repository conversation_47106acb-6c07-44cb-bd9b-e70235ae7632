<template>
  <el-dialog
    v-model="isDialogShow"
    :show-close="false"
    @close="closeAnswerDialog"
    class="dialog"
    style="max-height: 80%; overflow-y: auto"
    :z-index="10"
  >
    <div class="for-question">
      <b> {{ qList[page].userName }}</b>
      的提问
    </div>
    <div class="cq">
      “
      <b v-html="qList[page].associatedWords"></b>
      ”
      <!-- {{ qList[page].questionType }}？ -->
      <span
        v-html="qList[page].questionType"
        v-if="qList[page].questionType != '开放性问题'"
      ></span>
      <span v-html="qList[page].questionDescription" v-else></span>
      ？
    </div>
    <div class="date-answer">
      <div class="date">{{ qList[page].createTime }}</div>
      <div class="click-answer" @click="showMyAnswer = true">回答</div>
    </div>
    <el-divider class="line" />
    <div v-if="showMyAnswer" class="all-myanswer">
      <div class="for-myanswer">
        <b> {{ qList[page].userName }}</b>
        的回答
      </div>
      <CmpButton type="primary" class="add-klg" @click="showAddKlg = true"> +添加知识</CmpButton>
      <!-- <div class="klg-group" v-for="(item,index) in klgList" :key="index">
                <CmpButton type="info" class="klg" v-html="item.name"></CmpButton>
            </div> -->
      <div class="klg">
        <my-tag
          class="t"
          @delete="handleDelete"
          :tag-id="item.id"
          v-for="(item, index) in klgList"
          :key="index"
        >
          <el-tooltip popper-class="tooltip-width" :content="item.name" raw-content>
            <span v-html="item.name"></span>
          </el-tooltip>
        </my-tag>
      </div>
      <div class="ex-line">说明</div>
      <div class="ex">
        <!-- <el-input v-model="myinput" placeholder="解释说明"></el-input> -->
        <InlineEditor v-model="myinput"></InlineEditor>
      </div>
      <div class="btn-group">
        <CmpButton type="info" @click="showMyAnswer = false" class="btn">取消</CmpButton>
        <CmpButton type="primary" @click="handleAnswer" class="btn">提交</CmpButton>
      </div>
    </div>
    <div class="for-answer">
      <div class="answer-group" v-for="(ans, idx) in qList[page].answers" :key="idx">
        <div class="ans1">
          <b>{{ ans.userName }}</b>
          的回答
        </div>
        <!-- <div v-for="(item, index) in ans.answerKlgs" :key="index" class="klg-group">
                    <CmpButton type="info" class="klg" v-html="item.title"></CmpButton>
                </div> -->
        <div class="klg">
          <my-tag
            class="t"
            :tag-id="item.id"
            v-for="(item, index) in ans.answerKlgs"
            :key="index"
            :deletable="false"
          >
            <el-tooltip popper-class="tooltip-width" :content="item.title" raw-content>
              <span v-html="item.title"></span>
            </el-tooltip>
          </my-tag>
        </div>
        <div class="explain">{{ ans.answerExplanation }}</div>
        <!-- <div class="date">{{ ans.createTime }}</div> -->
        <div class="date-delete">
          <div class="date">{{ ans.createTime }}</div>
          <div class="click-delete" @click="handleDeleteAnswer(ans.answerId)" v-if="ans.canDelete">
            删除
          </div>
        </div>
        <div style="height: 30px"></div>
      </div>
    </div>
    <div class="foot">
      <el-pagination
        layout="prev, pager, next"
        @current-change="handleCurrentChange"
        :current-page="page + 1"
        :page-count="allpage"
        hide-on-single-page="true"
      />
    </div>
  </el-dialog>
  <add-klg v-if="showAddKlg" :showAddKlg="showAddKlg" @close="closeAddKlg" @submit="receiveTarget">
  </add-klg>
</template>
<script setup lang="ts">
// import CkEditorInline from '@/components/CkEditor/CkEditorInline.vue';
// import CkEditorClassic from '@/components/CkEditor/CkEditorClassic.vue';
import ClassicEditor from '@/components/editors/Vditor.vue';
import InlineEditor from '@/components/editors/VditorInline.vue';
import CmpButton from '@/components/CmpButton.vue';
import AddKlg from './AddKlg.vue';
import MyTag from './MyTag.vue';
import { saveAnswerApi, deleteAnswerApi } from '@/apis/learning';
const myinput = ref('');
const klgList = ref([]);
const emit = defineEmits(['close', 'refresh']);
const showAddKlg = ref(false);
const myinpur = ref('');
const page = ref(0);
const props = defineProps({
  qList: [],
  showqList: Boolean
});
const isDialogShow = ref(false);
const showMyAnswer = ref(false);
let allpage = props.qList.length;
isDialogShow.value = props.showqList;
// console.log(allpage)
watch(
  () => props.showqList,
  (newValue) => {
    isDialogShow.value = newValue;
    // console.log(props.showqList)
    // console.log(2,isDialogShow.value)
  },
  { deep: true, immediate: true }
);

watch(
  () => props.qList,
  (newValue) => {
    // console.log('vvvvvvvvvv',n)
    console.log(newValue);
    allpage = props.qList.length;
  },
  { deep: true, immediate: true }
);
const closeAddKlg = () => {
  showAddKlg.value = false;
};
const closeAnswerDialog = () => {
  emit('close', false);
  page.value = 0;
};
const receiveTarget = (tList, tForm: string) => {
  klgList.value.push(...tList);
  console.log(klgList);
};
const handleDelete = (aimId: string) => {
  console.log(aimId);
  klgList.value = klgList.value.filter((item) => {
    console.log(item.id != aimId);
    return item.id != aimId;
  });
  // 只维护selectedTList
  // const aimItem = klgList.value.find(t => t.id == aimId);
  // if (aimItem) {
  //     aimItem.isSelected = false;
  // }
  // const index = selectedTList.value.findIndex((i) => i.id == aimId);
  // if (index != -1) {
  //     selectedTList.value.splice(index, 1);
  // }
};
const handleCurrentChange = (data) => {
  page.value = data - 1;
};
const handleAnswer = () => {
  const answerKlgs = klgList.value.map((item) => item.id).join('@@');
  const params = {
    questionId: props.qList[page.value].questionId,
    answerKlgs: answerKlgs,
    answerExplanation: myinput.value
  };
  saveAnswerApi(params).then((res) => {
    if (res.success) {
      myinput.value = '';
      klgList.value = [];
      ElMessage({
        type: 'success',
        message: '保存成功'
      });
    } else {
      ElMessage.error(res.message);
    }
  });
  showMyAnswer.value = false;
  // closeAnswerDialog();
  const data = props.qList.map((item) => item.questionId).join(',');
  // console.log(data)
  emit('refresh', data);
};

const handleDeleteAnswer = (answerId) => {
  // console.log(answerId)
  // console.log(props.qList[page.value])
  deleteAnswerApi(answerId);
  const data = props.qList.map((item) => item.questionId).join(',');
  // console.log(data)
  emit('refresh', data);
};
onMounted(() => {});
</script>
<style scoped lang="less">
.dialog {
  .for-question {
    display: flex;
    margin-bottom: 8px;
    font-size: 12px;
  }

  .cq {
    margin-bottom: 8px;
  }

  .date-answer {
    display: flex;
    justify-content: space-between;
    font-size: 12px;

    .click-answer {
      color: var(--color-theme-project);

      &:hover {
        cursor: pointer;
      }
    }
  }

  .line {
    display: flex;
    margin-top: 12px;
    margin-bottom: 12px;
    width: 100%;
  }

  .all-myanswer {
    margin-left: 20px;
    margin-right: 20px;
    background-color: rgba(242, 242, 242, 1);
    padding: 8px;
    margin-bottom: 8px;

    .for-myanswer {
      margin-bottom: 8px;
    }

    .klg {
      display: flex;

      .t {
        display: flex;
        margin: 0 10px 10px 0;
        width: 90px;
        height: 24px;
      }
    }

    .add-klg {
      margin-bottom: 8px;
      width: 90px;
      height: 25px;
    }

    .ex-line {
      margin-bottom: 8px;
    }

    .ex {
      margin-bottom: 8px;
    }

    .btn-group {
      display: flex;
      justify-content: space-around;

      .btn {
        width: 150px;
        height: 40px;
        font-size: var(--fontsize-middle-project);
      }
    }
  }

  .for-answer {
    margin-left: 20px;
    margin-right: 20px;

    .answer-group {
      margin-bottom: 8px;

      .ans1 {
        margin-bottom: 8px;
      }

      .klg {
        display: flex;

        .t {
          display: flex;
          margin: 0 10px 10px 0;
          width: 90px;
          height: 24px;
        }
      }

      .explain {
      }

      .date-delete {
        display: flex;
        justify-content: space-between;
        font-size: 12px;

        .click-delete {
          &:hover {
            cursor: pointer;
          }
        }
      }
    }
  }

  .foot {
    display: flex;
    justify-content: center;
  }
}
</style>
